import pandas as pd

# 加载数据
df = pd.read_csv("merged_data_20250122_20250724.csv", encoding='utf-8')
print(f"原始数据: {len(df)} 条记录")

# 检查列名
print("\n列名检查:")
fund_flow_columns = [
    'FundFlow_主力净流入-净占比',
    'FundFlow_超大单净流入-净占比',
    'FundFlow_大单净流入-净占比',
    'FundFlow_中单净流入-净占比',
    'FundFlow_小单净流入-净占比'
]

for col in fund_flow_columns:
    if col in df.columns:
        print(f"✓ {col}")
    else:
        print(f"✗ {col}")

# 数据类型转换
for col in fund_flow_columns:
    df[col] = pd.to_numeric(df[col], errors='coerce')
df['收益率'] = pd.to_numeric(df['收益率'], errors='coerce')

# 删除空值
df_clean = df.dropna(subset=fund_flow_columns + ['收益率'])
print(f"清洗后数据: {len(df_clean)} 条记录")

# 验证条件1：主力净流入 > 10
condition1 = df_clean['FundFlow_主力净流入-净占比'] > 10
count1 = condition1.sum()
print(f"\n主力净流入 > 10: {count1} 条记录")

# 验证条件2：所有指标为正
condition2 = (df_clean[fund_flow_columns] > 0).all(axis=1)
count2 = condition2.sum()
print(f"所有指标为正: {count2} 条记录")

# 检查每个指标的正值数量
print(f"\n各指标正值统计:")
for col in fund_flow_columns:
    positive_count = (df_clean[col] > 0).sum()
    print(f"{col}: {positive_count} 条")

# 检查至少N个指标为正的情况
positive_counts = (df_clean[fund_flow_columns] > 0).sum(axis=1)
for i in range(5, 0, -1):
    count = (positive_counts >= i).sum()
    print(f"至少{i}个指标为正: {count} 条")

print("\n验证完成")
