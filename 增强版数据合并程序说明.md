# 增强版股票数据合并程序说明

## 修改概述

已成功修改 `final_data_merger.py` 程序，实现了涨停日期前5个交易日数据的合并功能。

## 主要修改内容

### 1. 新增交易日计算功能

```python
def get_trading_days_before(target_date_str, days_count=5):
    """
    获取指定日期前N个交易日的日期列表
    - 自动跳过周末等非交易日
    - 返回包含目标日期和前N个交易日的完整列表
    """
```

**功能特点:**
- 智能识别交易日（周一至周五）
- 自动跳过周末
- 支持自定义交易日数量
- 错误处理机制

### 2. 数据合并逻辑增强

**原始逻辑:**
- 只合并涨停当天的资金流向数据
- 列名格式: `资金流向_字段名`

**增强逻辑:**
- 合并涨停当天 + 前5个交易日的数据
- 列名格式: `资金流向_日期标识_字段名`
- 日期标识包括: `涨停日`、`前1日`、`前2日`...`前5日`

### 3. 新增数据字段

每条记录新增以下字段:
- `资金流向_匹配天数`: 实际匹配到的交易日数量
- `资金流向_涨停日_原始日期`: 涨停当天的具体日期
- `资金流向_前1日_原始日期`: 前1个交易日的具体日期
- `资金流向_前2日_原始日期`: 前2个交易日的具体日期
- ...以此类推

### 4. 统计报告增强

新增统计指标:
- 总匹配交易日数
- 平均每条记录匹配天数
- 数据完整性说明

## 数据结构示例

### 修改前
```
secID, secShortName, 涨停日期, 资金流向_收盘价, 资金流向_涨跌幅, 资金流向_主力净流入-净额
```

### 修改后
```
secID, secShortName, 涨停日期, 
资金流向_涨停日_收盘价, 资金流向_涨停日_涨跌幅, 资金流向_涨停日_主力净流入-净额, 资金流向_涨停日_原始日期,
资金流向_前1日_收盘价, 资金流向_前1日_涨跌幅, 资金流向_前1日_主力净流入-净额, 资金流向_前1日_原始日期,
资金流向_前2日_收盘价, 资金流向_前2日_涨跌幅, 资金流向_前2日_主力净流入-净额, 资金流向_前2日_原始日期,
...
资金流向_匹配天数
```

## 使用示例

### 场景: 股票000001在2025-06-18涨停

程序将自动合并以下日期的数据:
1. 2025-06-18 (涨停当天)
2. 2025-06-17 (前1个交易日)
3. 2025-06-16 (前2个交易日) 
4. 2025-06-13 (前3个交易日，跳过周末)
5. 2025-06-12 (前4个交易日)
6. 2025-06-11 (前5个交易日)

### 数据分析优势

1. **趋势分析**: 观察涨停前资金流向变化趋势
2. **预警信号**: 识别涨停前的异常资金流动
3. **模式识别**: 发现成功涨停的资金特征
4. **完整画像**: 构建股票完整的资金流向时间序列

## 运行方法

```bash
python final_data_merger.py
```

## 输出文件

程序将生成包含时间戳的合并文件:
- 文件名格式: `merged_data_YYYYMMDD_HHMMSS.csv`
- 包含所有原始字段 + 新增的多日资金流向数据

## 统计报告示例

```
数据合并统计报告
============================================================
总记录数: 1000
成功匹配: 950
未匹配记录: 50
缺失文件: 30
日期不匹配: 20
总匹配交易日数: 5700
匹配成功率: 95.00%
平均每条记录匹配天数: 6.00
说明：每条涨停记录最多匹配6天数据（涨停当天+前5个交易日）
============================================================
```

## 技术特点

1. **智能日期处理**: 自动识别和跳过非交易日
2. **数据完整性**: 保留所有原始日期信息
3. **错误处理**: 完善的异常处理机制
4. **向后兼容**: 保持原有数据结构的同时扩展新功能
5. **性能优化**: 高效的数据匹配算法

## 注意事项

1. 确保 `stock_fund_flow_data` 目录包含足够的历史数据
2. 资金流向文件需要包含涨停日期前5个交易日的数据
3. 如果某些交易日数据缺失，程序会继续处理并在统计中反映
4. 生成的文件可能较大，建议确保有足够的磁盘空间

## 文件清单

- `final_data_merger.py`: 增强版主程序
- `demo_enhanced_features.py`: 功能演示程序
- `增强版数据合并程序说明.md`: 本说明文档
