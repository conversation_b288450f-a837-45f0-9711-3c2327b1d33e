#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金流向与收益率关系深度分析程序
分析资金流向指标对收益率的影响，生成详细的统计报告
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
import os
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fund_flow_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FundFlowAnalyzer:
    """资金流向分析器"""
    
    def __init__(self, csv_file_path: str):
        """
        初始化分析器
        
        Args:
            csv_file_path: CSV文件路径
        """
        self.csv_file_path = csv_file_path
        self.df = None
        self.fund_flow_columns = [
            'FundFlow_主力净流入-净占比',
            'FundFlow_超大单净流入-净占比',
            'FundFlow_大单净流入-净占比',
            'FundFlow_中单净流入-净占比',
            'FundFlow_小单净流入-净占比'
        ]
        self.return_columns = ['收益率']  # 主要分析收益率列
        self.baseline_stats = {}
        self.analysis_results = []
        
    def load_data(self) -> bool:
        """
        加载CSV数据
        
        Returns:
            bool: 加载是否成功
        """
        try:
            logger.info(f"正在加载数据文件: {self.csv_file_path}")
            
            if not os.path.exists(self.csv_file_path):
                logger.error(f"文件不存在: {self.csv_file_path}")
                return False
                
            self.df = pd.read_csv(self.csv_file_path, encoding='utf-8')
            logger.info(f"数据加载成功，共 {len(self.df)} 条记录")
            
            # 检查必要的列是否存在
            missing_columns = []
            for col in self.fund_flow_columns + self.return_columns:
                if col not in self.df.columns:
                    missing_columns.append(col)
            
            if missing_columns:
                logger.error(f"缺少必要的列: {missing_columns}")
                return False
                
            # 数据清洗
            self._clean_data()
            return True
            
        except Exception as e:
            logger.error(f"数据加载失败: {str(e)}")
            return False
    
    def _clean_data(self):
        """数据清洗"""
        logger.info("开始数据清洗...")
        
        # 记录原始数据量
        original_count = len(self.df)
        
        # 删除收益率为空的记录
        self.df = self.df.dropna(subset=['收益率'])
        
        # 转换数据类型
        for col in self.fund_flow_columns:
            self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
        
        self.df['收益率'] = pd.to_numeric(self.df['收益率'], errors='coerce')
        
        # 删除转换失败的记录
        self.df = self.df.dropna(subset=self.fund_flow_columns + ['收益率'])
        
        cleaned_count = len(self.df)
        logger.info(f"数据清洗完成，从 {original_count} 条记录清洗为 {cleaned_count} 条记录")
        
    def calculate_baseline_stats(self):
        """计算基准统计数据（无筛选条件）"""
        logger.info("计算基准统计数据...")
        
        total_records = len(self.df)
        
        # 收益率>0%的统计
        positive_mask = self.df['收益率'] > 0
        positive_count = positive_mask.sum()
        positive_mean = self.df.loc[positive_mask, '收益率'].mean() if positive_count > 0 else 0
        positive_pct = (positive_count / total_records * 100) if total_records > 0 else 0
        
        # 收益率>1%的统计
        gt1_mask = self.df['收益率'] > 1
        gt1_count = gt1_mask.sum()
        gt1_mean = self.df.loc[gt1_mask, '收益率'].mean() if gt1_count > 0 else 0
        gt1_pct = (gt1_count / total_records * 100) if total_records > 0 else 0
        
        # 收益率>3%的统计
        gt3_mask = self.df['收益率'] > 3
        gt3_count = gt3_mask.sum()
        gt3_mean = self.df.loc[gt3_mask, '收益率'].mean() if gt3_count > 0 else 0
        gt3_pct = (gt3_count / total_records * 100) if total_records > 0 else 0
        
        self.baseline_stats = {
            'total_records': total_records,
            'positive': {'count': positive_count, 'mean': positive_mean, 'percentage': positive_pct},
            'gt1': {'count': gt1_count, 'mean': gt1_mean, 'percentage': gt1_pct},
            'gt3': {'count': gt3_count, 'mean': gt3_mean, 'percentage': gt3_pct}
        }
        
        logger.info(f"基准统计完成 - 总记录: {total_records}, 正收益: {positive_count}({positive_pct:.2f}%)")
        
    def generate_filter_conditions(self) -> List[Dict[str, Any]]:
        """
        生成智能筛选条件
        
        Returns:
            List[Dict]: 筛选条件列表
        """
        logger.info("生成筛选条件...")
        
        conditions = []
        
        # 1. 单指标分析 - 正值条件
        for col in self.fund_flow_columns:
            conditions.append({
                'name': f'{col} > 0',
                'description': f'{col}为正值',
                'filter_func': lambda df, column=col: df[column] > 0
            })
            
        # 2. 单指标分析 - 负值条件
        for col in self.fund_flow_columns:
            conditions.append({
                'name': f'{col} < 0',
                'description': f'{col}为负值',
                'filter_func': lambda df, column=col: df[column] < 0
            })
            
        # 3. 所有指标均为正值
        conditions.append({
            'name': 'All_Positive',
            'description': '所有资金流向指标均为正值',
            'filter_func': lambda df: (df[self.fund_flow_columns] > 0).all(axis=1)
        })
        
        # 4. 所有指标均为负值
        conditions.append({
            'name': 'All_Negative',
            'description': '所有资金流向指标均为负值',
            'filter_func': lambda df: (df[self.fund_flow_columns] < 0).all(axis=1)
        })
        
        # 5. 主力+超大单组合条件
        conditions.extend([
            {
                'name': 'MainForce_SuperLarge_Positive',
                'description': '主力净流入和超大单净流入均为正值',
                'filter_func': lambda df: (df['FundFlow_主力净流入-净占比'] > 0) & (df['FundFlow_超大单净流入-净占比'] > 0)
            },
            {
                'name': 'MainForce_SuperLarge_Negative',
                'description': '主力净流入和超大单净流入均为负值',
                'filter_func': lambda df: (df['FundFlow_主力净流入-净占比'] < 0) & (df['FundFlow_超大单净流入-净占比'] < 0)
            }
        ])
        
        # 6. 大单+中单+小单组合条件
        retail_columns = ['FundFlow_大单净流入-净占比', 'FundFlow_中单净流入-净占比', 'FundFlow_小单净流入-净占比']
        conditions.extend([
            {
                'name': 'Retail_All_Positive',
                'description': '大单、中单、小单净流入均为正值',
                'filter_func': lambda df: (df[retail_columns] > 0).all(axis=1)
            },
            {
                'name': 'Retail_All_Negative',
                'description': '大单、中单、小单净流入均为负值',
                'filter_func': lambda df: (df[retail_columns] < 0).all(axis=1)
            }
        ])
        
        # 7. 指标数值总和的阈值条件
        for threshold in [5, 10, 15, 20]:
            conditions.extend([
                {
                    'name': f'Sum_GT_{threshold}',
                    'description': f'所有资金流向指标总和 > {threshold}%',
                    'filter_func': lambda df, th=threshold: df[self.fund_flow_columns].sum(axis=1) > th
                },
                {
                    'name': f'Sum_LT_Minus_{threshold}',
                    'description': f'所有资金流向指标总和 < -{threshold}%',
                    'filter_func': lambda df, th=threshold: df[self.fund_flow_columns].sum(axis=1) < -th
                }
            ])

        # 8. 指标中正值数量的条件
        for min_positive in [2, 3, 4]:
            conditions.append({
                'name': f'AtLeast_{min_positive}_Positive',
                'description': f'至少{min_positive}个指标为正值',
                'filter_func': lambda df, mp=min_positive: (df[self.fund_flow_columns] > 0).sum(axis=1) >= mp
            })

        # 9. 主力资金强度条件
        for threshold in [5, 10, 15]:
            conditions.extend([
                {
                    'name': f'MainForce_GT_{threshold}',
                    'description': f'主力净流入净占比 > {threshold}%',
                    'filter_func': lambda df, th=threshold: df['FundFlow_主力净流入-净占比'] > th
                },
                {
                    'name': f'MainForce_LT_Minus_{threshold}',
                    'description': f'主力净流入净占比 < -{threshold}%',
                    'filter_func': lambda df, th=threshold: df['FundFlow_主力净流入-净占比'] < -th
                }
            ])

        logger.info(f"生成了 {len(conditions)} 个筛选条件")
        return conditions

    def analyze_condition(self, condition: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析单个筛选条件的效果

        Args:
            condition: 筛选条件

        Returns:
            Dict: 分析结果
        """
        try:
            # 应用筛选条件
            filtered_df = self.df[condition['filter_func'](self.df)]
            total_filtered = len(filtered_df)

            if total_filtered == 0:
                return {
                    'condition_name': condition['name'],
                    'description': condition['description'],
                    'total_records': 0,
                    'positive': {'count': 0, 'mean': 0, 'percentage': 0, 'improvement': 0},
                    'gt1': {'count': 0, 'mean': 0, 'percentage': 0, 'improvement': 0},
                    'gt3': {'count': 0, 'mean': 0, 'percentage': 0, 'improvement': 0}
                }

            # 计算各种收益率条件的统计
            results = {
                'condition_name': condition['name'],
                'description': condition['description'],
                'total_records': total_filtered
            }

            # 收益率>0%的统计
            positive_mask = filtered_df['收益率'] > 0
            positive_count = positive_mask.sum()
            positive_mean = filtered_df.loc[positive_mask, '收益率'].mean() if positive_count > 0 else 0
            positive_pct = (positive_count / total_filtered * 100) if total_filtered > 0 else 0
            positive_improvement = positive_pct - self.baseline_stats['positive']['percentage']

            results['positive'] = {
                'count': positive_count,
                'mean': positive_mean,
                'percentage': positive_pct,
                'improvement': positive_improvement
            }

            # 收益率>1%的统计
            gt1_mask = filtered_df['收益率'] > 1
            gt1_count = gt1_mask.sum()
            gt1_mean = filtered_df.loc[gt1_mask, '收益率'].mean() if gt1_count > 0 else 0
            gt1_pct = (gt1_count / total_filtered * 100) if total_filtered > 0 else 0
            gt1_improvement = gt1_pct - self.baseline_stats['gt1']['percentage']

            results['gt1'] = {
                'count': gt1_count,
                'mean': gt1_mean,
                'percentage': gt1_pct,
                'improvement': gt1_improvement
            }

            # 收益率>3%的统计
            gt3_mask = filtered_df['收益率'] > 3
            gt3_count = gt3_mask.sum()
            gt3_mean = filtered_df.loc[gt3_mask, '收益率'].mean() if gt3_count > 0 else 0
            gt3_pct = (gt3_count / total_filtered * 100) if total_filtered > 0 else 0
            gt3_improvement = gt3_pct - self.baseline_stats['gt3']['percentage']

            results['gt3'] = {
                'count': gt3_count,
                'mean': gt3_mean,
                'percentage': gt3_pct,
                'improvement': gt3_improvement
            }

            return results

        except Exception as e:
            logger.error(f"分析条件 {condition['name']} 时出错: {str(e)}")
            return None

    def run_analysis(self):
        """运行完整分析"""
        logger.info("开始运行完整分析...")

        # 计算基准统计
        self.calculate_baseline_stats()

        # 生成筛选条件
        conditions = self.generate_filter_conditions()

        # 分析每个条件
        logger.info("开始分析各个筛选条件...")
        for i, condition in enumerate(conditions, 1):
            logger.info(f"分析进度: {i}/{len(conditions)} - {condition['name']}")
            result = self.analyze_condition(condition)
            if result:
                self.analysis_results.append(result)

        logger.info(f"分析完成，共分析了 {len(self.analysis_results)} 个有效条件")

    def generate_report(self) -> str:
        """
        生成详细的分析报告

        Returns:
            str: 报告内容
        """
        logger.info("生成分析报告...")

        report = []
        report.append("=" * 80)
        report.append("资金流向与收益率关系深度分析报告")
        report.append("=" * 80)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"数据文件: {self.csv_file_path}")
        report.append("")

        # 数据概览
        report.append("1. 数据概览")
        report.append("-" * 40)
        report.append(f"总记录数: {self.baseline_stats['total_records']}")
        report.append(f"分析的资金流向指标: {len(self.fund_flow_columns)} 个")
        for col in self.fund_flow_columns:
            report.append(f"  - {col}")
        report.append("")

        # 基准统计
        report.append("2. 基准统计（无筛选条件）")
        report.append("-" * 40)
        baseline = self.baseline_stats
        report.append(f"收益率 > 0%: {baseline['positive']['count']} 条 ({baseline['positive']['percentage']:.2f}%), 平均收益率: {baseline['positive']['mean']:.2f}%")
        report.append(f"收益率 > 1%: {baseline['gt1']['count']} 条 ({baseline['gt1']['percentage']:.2f}%), 平均收益率: {baseline['gt1']['mean']:.2f}%")
        report.append(f"收益率 > 3%: {baseline['gt3']['count']} 条 ({baseline['gt3']['percentage']:.2f}%), 平均收益率: {baseline['gt3']['mean']:.2f}%")
        report.append("")

        # 最优筛选条件推荐
        report.append("3. 最优筛选条件推荐")
        report.append("-" * 40)

        # 按不同指标排序找出最优条件
        valid_results = [r for r in self.analysis_results if r['total_records'] >= 5]  # 至少5条记录才有意义

        if valid_results:
            # 按收益率>0%的改善程度排序
            best_positive = sorted(valid_results, key=lambda x: x['positive']['improvement'], reverse=True)[:5]
            report.append("3.1 收益率>0%改善效果最佳的条件:")
            for i, result in enumerate(best_positive, 1):
                report.append(f"  {i}. {result['description']}")
                report.append(f"     筛选后记录: {result['total_records']} 条")
                report.append(f"     正收益率: {result['positive']['percentage']:.2f}% (改善: +{result['positive']['improvement']:.2f}%)")
                report.append("")

            # 按收益率>3%的改善程度排序
            best_gt3 = sorted(valid_results, key=lambda x: x['gt3']['improvement'], reverse=True)[:5]
            report.append("3.2 收益率>3%改善效果最佳的条件:")
            for i, result in enumerate(best_gt3, 1):
                report.append(f"  {i}. {result['description']}")
                report.append(f"     筛选后记录: {result['total_records']} 条")
                report.append(f"     >3%收益率: {result['gt3']['percentage']:.2f}% (改善: +{result['gt3']['improvement']:.2f}%)")
                report.append("")
        else:
            report.append("未找到有效的筛选条件（记录数不足）")
            report.append("")

        return "\n".join(report)

    def export_results_to_csv(self, output_file: str = None):
        """
        导出分析结果到CSV文件

        Args:
            output_file: 输出文件路径，默认为自动生成
        """
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f'fund_flow_analysis_results_{timestamp}.csv'

        logger.info(f"导出分析结果到: {output_file}")

        # 准备CSV数据
        csv_data = []

        # 添加基准数据
        baseline = self.baseline_stats
        csv_data.append({
            '筛选条件描述': '基准（无筛选）',
            '筛选后记录数量': baseline['total_records'],
            '收益率>0%_数量': baseline['positive']['count'],
            '收益率>0%_均值': round(baseline['positive']['mean'], 2),
            '收益率>0%_百分比': round(baseline['positive']['percentage'], 2),
            '收益率>1%_数量': baseline['gt1']['count'],
            '收益率>1%_均值': round(baseline['gt1']['mean'], 2),
            '收益率>1%_百分比': round(baseline['gt1']['percentage'], 2),
            '收益率>3%_数量': baseline['gt3']['count'],
            '收益率>3%_均值': round(baseline['gt3']['mean'], 2),
            '收益率>3%_百分比': round(baseline['gt3']['percentage'], 2),
            '相对基准改善率_正收益': 0,
            '相对基准改善率_1%以上': 0,
            '相对基准改善率_3%以上': 0
        })

        # 添加筛选条件结果
        for result in self.analysis_results:
            if result['total_records'] > 0:  # 只导出有记录的结果
                csv_data.append({
                    '筛选条件描述': result['description'],
                    '筛选后记录数量': result['total_records'],
                    '收益率>0%_数量': result['positive']['count'],
                    '收益率>0%_均值': round(result['positive']['mean'], 2),
                    '收益率>0%_百分比': round(result['positive']['percentage'], 2),
                    '收益率>1%_数量': result['gt1']['count'],
                    '收益率>1%_均值': round(result['gt1']['mean'], 2),
                    '收益率>1%_百分比': round(result['gt1']['percentage'], 2),
                    '收益率>3%_数量': result['gt3']['count'],
                    '收益率>3%_均值': round(result['gt3']['mean'], 2),
                    '收益率>3%_百分比': round(result['gt3']['percentage'], 2),
                    '相对基准改善率_正收益': round(result['positive']['improvement'], 2),
                    '相对基准改善率_1%以上': round(result['gt1']['improvement'], 2),
                    '相对基准改善率_3%以上': round(result['gt3']['improvement'], 2)
                })

        # 创建DataFrame并保存
        results_df = pd.DataFrame(csv_data)

        # 按改善率排序
        results_df = results_df.sort_values('相对基准改善率_3%以上', ascending=False)

        try:
            results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            logger.info(f"结果已成功导出到: {output_file}")
            return output_file
        except Exception as e:
            logger.error(f"导出CSV文件失败: {str(e)}")
            return None

    def save_report(self, report_content: str, output_file: str = None):
        """
        保存分析报告到文件

        Args:
            report_content: 报告内容
            output_file: 输出文件路径，默认为自动生成
        """
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f'fund_flow_analysis_report_{timestamp}.txt'

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            logger.info(f"分析报告已保存到: {output_file}")
            return output_file
        except Exception as e:
            logger.error(f"保存报告失败: {str(e)}")
            return None


def main():
    """主函数"""
    # 配置文件路径
    csv_file_path = r"d:\Andy\coding\gupiao_huice\merged_data_20250122_20250724.csv"

    logger.info("=" * 60)
    logger.info("资金流向与收益率关系深度分析程序启动")
    logger.info("=" * 60)

    try:
        # 创建分析器
        analyzer = FundFlowAnalyzer(csv_file_path)

        # 加载数据
        if not analyzer.load_data():
            logger.error("数据加载失败，程序退出")
            return

        # 运行分析
        analyzer.run_analysis()

        # 生成报告
        report = analyzer.generate_report()

        # 保存报告
        report_file = analyzer.save_report(report)

        # 导出CSV结果
        csv_file = analyzer.export_results_to_csv()

        # 输出摘要
        logger.info("=" * 60)
        logger.info("分析完成！")
        logger.info("=" * 60)
        logger.info(f"分析了 {len(analyzer.analysis_results)} 个筛选条件")
        logger.info(f"基准数据 - 总记录: {analyzer.baseline_stats['total_records']}")
        logger.info(f"基准数据 - 正收益率: {analyzer.baseline_stats['positive']['percentage']:.2f}%")

        if report_file:
            logger.info(f"详细报告: {report_file}")
        if csv_file:
            logger.info(f"CSV结果: {csv_file}")

        # 显示部分报告内容
        print("\n" + "=" * 80)
        print("分析报告摘要")
        print("=" * 80)
        print(report[:2000] + "..." if len(report) > 2000 else report)

    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    main()
